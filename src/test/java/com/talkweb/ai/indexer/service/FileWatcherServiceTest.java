package com.talkweb.ai.indexer.service;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import static org.junit.jupiter.api.Assertions.*;

class FileWatcherServiceTest {

    @TempDir
    Path tempDir;

    private FileWatcherService fileWatcherService;

    @BeforeEach
    void setUp() throws IOException {
        fileWatcherService = new FileWatcherService(tempDir);
    }

    @AfterEach
    void tearDown() {
        if (fileWatcherService != null) {
            fileWatcherService.stop();
        }
    }

    @Test
    void testFileCreatedEvent() throws IOException, InterruptedException {
        // Arrange
        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<Path> createdFilePath = new AtomicReference<>();

        fileWatcherService.addFileCreatedListener(path -> {
            createdFilePath.set(path);
            latch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Test content");

        // Assert
        boolean eventReceived = latch.await(5, TimeUnit.SECONDS);
        assertTrue(eventReceived, "File created event should be received");
        assertEquals(testFile, createdFilePath.get(), "Created file path should match");
    }

    @Test
    void testFileModifiedEvent() throws IOException, InterruptedException {
        // Arrange
        CountDownLatch createdLatch = new CountDownLatch(1);
        CountDownLatch modifiedLatch = new CountDownLatch(1);
        AtomicReference<Path> modifiedFilePath = new AtomicReference<>();

        fileWatcherService.addFileCreatedListener(path -> createdLatch.countDown());
        fileWatcherService.addFileModifiedListener(path -> {
            modifiedFilePath.set(path);
            modifiedLatch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        // Create file after watcher starts
        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Initial content");

        // Wait for creation event
        assertTrue(createdLatch.await(5, TimeUnit.SECONDS), "File creation should be detected");

        // Add a small delay to ensure the file is stable
        Thread.sleep(100);

        // Now modify the file
        Files.writeString(testFile, "Modified content");

        // Assert
        boolean eventReceived = modifiedLatch.await(10, TimeUnit.SECONDS);
        assertTrue(eventReceived, "File modified event should be received within the timeout");
        assertEquals(testFile, modifiedFilePath.get(), "The path of the modified file should be correct");
    }

    @Test
    void testFileDeletedEvent() throws IOException, InterruptedException {
        // Arrange
        CountDownLatch createdLatch = new CountDownLatch(1);
        CountDownLatch deletedLatch = new CountDownLatch(1);
        AtomicReference<Path> deletedFilePath = new AtomicReference<>();

        fileWatcherService.addFileCreatedListener(path -> createdLatch.countDown());
        fileWatcherService.addFileDeletedListener(path -> {
            deletedFilePath.set(path);
            deletedLatch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        // Create file after watcher starts
        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Test content");

        // Wait for creation event
        assertTrue(createdLatch.await(5, TimeUnit.SECONDS), "File creation should be detected");

        // Add a small delay to ensure the file is stable
        Thread.sleep(100);

        // Now delete the file
        Files.delete(testFile);

        // Assert
        boolean eventReceived = deletedLatch.await(10, TimeUnit.SECONDS);
        assertTrue(eventReceived, "File deleted event should be received within the timeout");
        assertEquals(testFile, deletedFilePath.get(), "The path of the deleted file should be correct");
    }

    @Test
    void testMultipleListeners() throws IOException, InterruptedException {
        // Arrange
        CountDownLatch createdLatch = new CountDownLatch(2);

        fileWatcherService.addFileCreatedListener(path -> createdLatch.countDown());
        fileWatcherService.addFileCreatedListener(path -> createdLatch.countDown());

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Test content");

        // Assert
        boolean allEventsReceived = createdLatch.await(5, TimeUnit.SECONDS);
        assertTrue(allEventsReceived, "All file created events should be received by multiple listeners");
    }

    @Test
    void testStartStopService() throws IOException {
        // Act & Assert - No exception should be thrown
        fileWatcherService.start();
        fileWatcherService.start(); // Starting again should be no-op
        fileWatcherService.stop();
        fileWatcherService.stop(); // Stopping again should be no-op
    }

    @Test
    void testListenerExceptionHandling() throws IOException, InterruptedException {
        // Arrange
        CountDownLatch latch = new CountDownLatch(1);

        // First listener throws exception
        fileWatcherService.addFileCreatedListener(path -> {
            throw new RuntimeException("Test exception");
        });

        // Second listener should still be called
        fileWatcherService.addFileCreatedListener(path -> {
            latch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Path testFile = tempDir.resolve("test-file.txt");
        Files.writeString(testFile, "Test content");

        // Assert
        boolean eventReceived = latch.await(5, TimeUnit.SECONDS);
        assertTrue(eventReceived, "Second listener should be called even if first listener throws exception");
    }

    @Test
    void testWatcherModeDetection() throws IOException {
        // Test that we can detect which mode the watcher is using
        fileWatcherService.start();

        // The watcher should be running
        assertTrue(fileWatcherService.isRunning(), "Watcher should be running after start");

        // Should have some mode (native or polling)
        boolean hasMode = fileWatcherService.isUsingNativeWatcher() || !fileWatcherService.isUsingNativeWatcher();
        assertTrue(hasMode, "Watcher should have a defined mode");

        fileWatcherService.stop();
        assertFalse(fileWatcherService.isRunning(), "Watcher should not be running after stop");
    }

    @Test
    void testPollingIntervalAdjustment() throws IOException, InterruptedException {
        // Test that polling interval can be monitored
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        long initialInterval = fileWatcherService.getCurrentPollInterval();
        assertTrue(initialInterval > 0, "Poll interval should be positive");

        // Give some time for potential adjustments
        Thread.sleep(1000);

        long currentInterval = fileWatcherService.getCurrentPollInterval();
        assertTrue(currentInterval > 0, "Poll interval should remain positive");
    }

    @Test
    void testFileTrackingCount() throws IOException, InterruptedException {
        // Test file tracking functionality
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        int initialCount = fileWatcherService.getTrackedFileCount();

        // Create a test file
        Path testFile = tempDir.resolve("tracking-test.txt");
        Files.writeString(testFile, "Test content");

        // Give time for the file to be detected
        Thread.sleep(1000);

        int newCount = fileWatcherService.getTrackedFileCount();
        assertTrue(newCount >= initialCount, "File count should not decrease after adding file");
    }

    @Test
    void testConcurrentListenerExecution() throws IOException, InterruptedException {
        // Test that multiple listeners can execute concurrently without issues
        int listenerCount = 5;
        CountDownLatch latch = new CountDownLatch(listenerCount);

        for (int i = 0; i < listenerCount; i++) {
            final int listenerId = i;
            fileWatcherService.addFileCreatedListener(path -> {
                try {
                    // Simulate some processing time
                    Thread.sleep(10);
                    latch.countDown();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }

        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Path testFile = tempDir.resolve("concurrent-test.txt");
        Files.writeString(testFile, "Test content");

        boolean allListenersExecuted = latch.await(10, TimeUnit.SECONDS);
        assertTrue(allListenersExecuted, "All listeners should execute within timeout");
    }

    @Test
    void testFileModifiedEventWithEnhancedStability() throws IOException, InterruptedException {
        // Arrange
        CountDownLatch createdLatch = new CountDownLatch(1);
        CountDownLatch modifiedLatch = new CountDownLatch(1);
        AtomicReference<Path> modifiedFilePath = new AtomicReference<>();

        fileWatcherService.addFileCreatedListener(path -> createdLatch.countDown());
        fileWatcherService.addFileModifiedListener(path -> {
            modifiedFilePath.set(path);
            modifiedLatch.countDown();
        });

        // Act
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        // Create test file after watcher starts
        Path testFile = tempDir.resolve("stable-test-" + System.currentTimeMillis() + ".txt");
        Files.writeString(testFile, "Initial content");

        // Wait for creation event
        assertTrue(createdLatch.await(5, TimeUnit.SECONDS), "File creation should be detected");

        // Ensure file is stable before modification
        Thread.sleep(200);

        // Modify file with enhanced stability
        Files.writeString(testFile, "Modified content with timestamp: " + System.currentTimeMillis());

        // Assert with enhanced waiting
        boolean eventReceived = modifiedLatch.await(10, TimeUnit.SECONDS);
        assertTrue(eventReceived, "File modified event should be received within timeout");
        assertEquals(testFile, modifiedFilePath.get(), "Modified file path should match");
    }

    @Test
    void testRobustFileOperations() throws IOException, InterruptedException {
        // Test handling of rapid file operations
        CountDownLatch createdLatch = new CountDownLatch(1);
        CountDownLatch modifiedLatch = new CountDownLatch(1);
        CountDownLatch deletedLatch = new CountDownLatch(1);

        fileWatcherService.addFileCreatedListener(path -> createdLatch.countDown());
        fileWatcherService.addFileModifiedListener(path -> modifiedLatch.countDown());
        fileWatcherService.addFileDeletedListener(path -> deletedLatch.countDown());

        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher service should start within 5 seconds");

        Path testFile = tempDir.resolve("robust-test.txt");

        // Rapid operations
        Files.writeString(testFile, "Initial");
        assertTrue(createdLatch.await(5, TimeUnit.SECONDS), "File creation should be detected");

        Thread.sleep(100); // Small delay between operations
        Files.writeString(testFile, "Modified");
        assertTrue(modifiedLatch.await(5, TimeUnit.SECONDS), "File modification should be detected");

        Thread.sleep(100);
        Files.delete(testFile);
        assertTrue(deletedLatch.await(5, TimeUnit.SECONDS), "File deletion should be detected");
    }

    /**
     * Helper method to wait for a condition with timeout
     */
    private boolean waitForCondition(java.util.function.BooleanSupplier condition, long timeoutMs) {
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            if (condition.getAsBoolean()) {
                return true;
            }
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        return false;
    }

    // ========== 边界条件测试 ==========

    @Test
    void testWatcherWithNonExistentPath() {
        // Test that watcher throws exception for non-existent path
        Path nonExistentPath = tempDir.resolve("non-existent-directory");
        assertThrows(IllegalArgumentException.class, () -> {
            new FileWatcherService(nonExistentPath);
        }, "Should throw IllegalArgumentException for non-existent path");
    }

    @Test
    void testWatcherWithFilePath() throws IOException {
        // Test that watcher throws exception when path is a file, not directory
        Path filePath = tempDir.resolve("test-file.txt");
        Files.writeString(filePath, "test content");

        assertThrows(IllegalArgumentException.class, () -> {
            new FileWatcherService(filePath);
        }, "Should throw IllegalArgumentException when path is a file");
    }

    @Test
    void testEmptyDirectory() throws IOException, InterruptedException {
        // Test watcher behavior with empty directory
        Path emptyDir = tempDir.resolve("empty-dir");
        Files.createDirectory(emptyDir);

        FileWatcherService emptyDirWatcher = new FileWatcherService(emptyDir);
        try {
            emptyDirWatcher.start();
            assertTrue(emptyDirWatcher.awaitStarted(5, TimeUnit.SECONDS), "Watcher should start for empty directory");
            assertEquals(0, emptyDirWatcher.getTrackedFileCount(), "Empty directory should have 0 tracked files");
        } finally {
            emptyDirWatcher.stop();
        }
    }

    @Test
    void testLargeNumberOfFiles() throws IOException, InterruptedException {
        // Test watcher performance with large number of files
        int fileCount = 100;
        CountDownLatch createdLatch = new CountDownLatch(fileCount);

        fileWatcherService.addFileCreatedListener(path -> createdLatch.countDown());
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher should start");

        // Create many files rapidly
        for (int i = 0; i < fileCount; i++) {
            Path testFile = tempDir.resolve("bulk-test-" + i + ".txt");
            Files.writeString(testFile, "Content " + i);
        }

        // Should handle all file creations within reasonable time
        boolean allDetected = createdLatch.await(30, TimeUnit.SECONDS);
        assertTrue(allDetected, "Should detect all " + fileCount + " file creations");

        // Verify tracked file count
        assertTrue(fileWatcherService.getTrackedFileCount() >= fileCount,
                "Should track at least " + fileCount + " files");
    }

    @Test
    void testFileNameWithSpecialCharacters() throws IOException, InterruptedException {
        // Test handling of files with special characters in names
        String[] specialNames = {
            "file with spaces.txt",
            "file-with-dashes.txt",
            "file_with_underscores.txt",
            "file.with.dots.txt",
            "文件中文名.txt",
            "file(with)parentheses.txt"
        };

        CountDownLatch latch = new CountDownLatch(specialNames.length);
        fileWatcherService.addFileCreatedListener(path -> latch.countDown());

        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher should start");

        for (String name : specialNames) {
            Path testFile = tempDir.resolve(name);
            Files.writeString(testFile, "Special content");
        }

        boolean allDetected = latch.await(10, TimeUnit.SECONDS);
        assertTrue(allDetected, "Should detect files with special characters in names");
    }

    // ========== 性能和压力测试 ==========

    @Test
    void testRapidFileOperations() throws IOException, InterruptedException {
        // Test handling of very rapid file operations
        int operationCount = 50;
        CountDownLatch allOperationsLatch = new CountDownLatch(operationCount * 2); // create + modify

        fileWatcherService.addFileCreatedListener(path -> allOperationsLatch.countDown());
        fileWatcherService.addFileModifiedListener(path -> allOperationsLatch.countDown());

        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher should start");

        // Perform rapid operations
        for (int i = 0; i < operationCount; i++) {
            Path testFile = tempDir.resolve("rapid-" + i + ".txt");
            Files.writeString(testFile, "Initial");
            Files.writeString(testFile, "Modified");
        }

        // Should handle rapid operations without missing events
        boolean allDetected = allOperationsLatch.await(20, TimeUnit.SECONDS);
        assertTrue(allDetected, "Should handle rapid file operations");
    }

    @Test
    void testLongRunningWatcher() throws IOException, InterruptedException {
        // Test watcher stability over extended period
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher should start");

        long initialInterval = fileWatcherService.getCurrentPollInterval();
        assertTrue(fileWatcherService.isRunning(), "Watcher should be running");

        // Simulate long running by creating files periodically
        for (int i = 0; i < 10; i++) {
            Path testFile = tempDir.resolve("long-running-" + i + ".txt");
            Files.writeString(testFile, "Content " + i);
            Thread.sleep(100); // Small delay between operations
        }

        // Verify watcher is still stable
        assertTrue(fileWatcherService.isRunning(), "Watcher should still be running after extended use");
        assertTrue(fileWatcherService.getCurrentPollInterval() > 0, "Poll interval should remain positive");
    }

    // ========== 资源管理测试 ==========

    @Test
    void testResourceCleanupOnStop() throws IOException, InterruptedException {
        // Test proper resource cleanup when stopping
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher should start");
        assertTrue(fileWatcherService.isRunning(), "Watcher should be running");

        // Create some files to track
        for (int i = 0; i < 5; i++) {
            Path testFile = tempDir.resolve("cleanup-test-" + i + ".txt");
            Files.writeString(testFile, "Content " + i);
        }

        Thread.sleep(500); // Allow files to be tracked
        assertTrue(fileWatcherService.getTrackedFileCount() > 0, "Should have tracked files");

        // Stop and verify cleanup
        fileWatcherService.stop();
        assertFalse(fileWatcherService.isRunning(), "Watcher should not be running after stop");

        // Note: tracked file count may not be cleared immediately as it's used for state management
    }

    @Test
    void testMultipleStartStopCycles() throws IOException, InterruptedException {
        // Test multiple start/stop cycles for resource leaks
        for (int cycle = 0; cycle < 3; cycle++) {
            fileWatcherService.start();
            assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS),
                    "Watcher should start in cycle " + cycle);
            assertTrue(fileWatcherService.isRunning(), "Watcher should be running in cycle " + cycle);

            // Create a test file
            Path testFile = tempDir.resolve("cycle-" + cycle + ".txt");
            Files.writeString(testFile, "Cycle " + cycle);

            Thread.sleep(200); // Allow processing

            fileWatcherService.stop();
            assertFalse(fileWatcherService.isRunning(), "Watcher should be stopped in cycle " + cycle);

            Thread.sleep(100); // Brief pause between cycles
        }
    }

    // ========== 异常处理和恢复测试 ==========

    @Test
    void testWatcherRecoveryAfterDirectoryDeletion() throws IOException, InterruptedException {
        // Test watcher behavior when watched directory is deleted and recreated
        Path subDir = tempDir.resolve("deletable-dir");
        Files.createDirectory(subDir);

        FileWatcherService subDirWatcher = new FileWatcherService(subDir);
        try {
            subDirWatcher.start();
            assertTrue(subDirWatcher.awaitStarted(5, TimeUnit.SECONDS), "Watcher should start");

            // Create a file to verify watcher is working
            Path testFile = subDir.resolve("test.txt");
            Files.writeString(testFile, "test");

            Thread.sleep(500); // Allow processing

            // Delete the directory
            Files.delete(testFile);
            Files.delete(subDir);

            Thread.sleep(1000); // Allow watcher to detect deletion

            // Recreate directory
            Files.createDirectory(subDir);

            Thread.sleep(500); // Allow watcher to recover

            // Watcher should handle this gracefully (may switch to error state but not crash)
            // The exact behavior depends on implementation details
        } finally {
            subDirWatcher.stop();
            // Cleanup
            if (Files.exists(subDir)) {
                try {
                    Files.delete(subDir);
                } catch (IOException e) {
                    // Ignore cleanup errors
                }
            }
        }
    }

    @Test
    void testListenerRemovalAndMemoryManagement() throws IOException, InterruptedException {
        // Test that listeners can be managed properly (though current API doesn't support removal)
        List<Consumer<Path>> testListeners = new ArrayList<>();

        // Add multiple listeners
        for (int i = 0; i < 10; i++) {
            Consumer<Path> listener = path -> {
                // Simple listener that does nothing
            };
            testListeners.add(listener);
            fileWatcherService.addFileCreatedListener(listener);
        }

        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher should start");

        // Create a file to trigger listeners
        Path testFile = tempDir.resolve("listener-test.txt");
        Files.writeString(testFile, "test content");

        Thread.sleep(500); // Allow processing

        // Stop watcher - this should clean up listener references
        fileWatcherService.stop();

        // Clear our references
        testListeners.clear();

        // Note: Current implementation doesn't provide listener removal,
        // but stopping the watcher should prevent further listener calls
    }

    // ========== API覆盖测试 ==========

    @Test
    void testAllPublicApiMethods() throws IOException, InterruptedException {
        // Test all public API methods for basic functionality

        // Test initial state
        assertFalse(fileWatcherService.isRunning(), "Watcher should not be running initially");
        assertTrue(fileWatcherService.getCurrentPollInterval() > 0, "Poll interval should be positive");
        assertTrue(fileWatcherService.getTrackedFileCount() >= 0, "Tracked file count should be non-negative");

        // Test start
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher should start");
        assertTrue(fileWatcherService.isRunning(), "Watcher should be running after start");

        // Test mode detection
        boolean hasMode = fileWatcherService.isUsingNativeWatcher() || !fileWatcherService.isUsingNativeWatcher();
        assertTrue(hasMode, "Watcher should have a defined mode");

        // Test file operations
        Path testFile = tempDir.resolve("api-test.txt");
        Files.writeString(testFile, "test content");

        Thread.sleep(500); // Allow processing

        // Test tracking
        int trackedCount = fileWatcherService.getTrackedFileCount();
        assertTrue(trackedCount >= 0, "Tracked file count should be non-negative");

        // Test stop
        fileWatcherService.stop();
        assertFalse(fileWatcherService.isRunning(), "Watcher should not be running after stop");
    }

    @Test
    void testPollingIntervalBehavior() throws IOException, InterruptedException {
        // Test polling interval behavior under different conditions
        fileWatcherService.start();
        assertTrue(fileWatcherService.awaitStarted(5, TimeUnit.SECONDS), "Watcher should start");

        long initialInterval = fileWatcherService.getCurrentPollInterval();
        assertTrue(initialInterval > 0, "Initial poll interval should be positive");

        // Create files to trigger processing
        for (int i = 0; i < 5; i++) {
            Path testFile = tempDir.resolve("interval-test-" + i + ".txt");
            Files.writeString(testFile, "Content " + i);
            Thread.sleep(100);
        }

        // Allow some processing time
        Thread.sleep(2000);

        long currentInterval = fileWatcherService.getCurrentPollInterval();
        assertTrue(currentInterval > 0, "Poll interval should remain positive");

        // The interval may have adjusted based on system behavior
        // We just verify it remains within reasonable bounds
        assertTrue(currentInterval >= 100 && currentInterval <= 5000,
                "Poll interval should be within reasonable bounds");
    }
}
