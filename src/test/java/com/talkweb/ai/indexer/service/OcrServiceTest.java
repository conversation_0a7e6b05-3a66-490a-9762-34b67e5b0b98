package com.talkweb.ai.indexer.service;

import com.talkweb.ai.indexer.config.OcrConfiguration;
import com.talkweb.ai.indexer.model.OcrResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * OCR服务测试类
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class OcrServiceTest {

    @Mock
    private OcrConfiguration ocrConfig;

    private OcrService ocrService;

    @BeforeEach
    void setUp() {
        // 设置默认配置
        lenient().when(ocrConfig.isEnabled()).thenReturn(true);
        lenient().when(ocrConfig.getLanguageString()).thenReturn("eng+chi_sim");
        lenient().when(ocrConfig.getLanguages()).thenReturn(List.of("eng", "chi_sim"));
        lenient().when(ocrConfig.getPageSegmentationMode()).thenReturn(3);
        lenient().when(ocrConfig.getOcrEngineMode()).thenReturn(3);
        lenient().when(ocrConfig.getConfidenceThreshold()).thenReturn(60);
        lenient().when(ocrConfig.getTimeoutSeconds()).thenReturn(30);
        lenient().when(ocrConfig.isPreprocessingEnabled()).thenReturn(true);
        lenient().when(ocrConfig.isVerboseLogging()).thenReturn(false);
        lenient().when(ocrConfig.getCustomVariables()).thenReturn(java.util.Map.of());
        lenient().when(ocrConfig.getDataPath()).thenReturn(null);

        ocrService = new OcrService(ocrConfig);
    }

    @Test
    void testServiceInitializationWhenEnabled() {
        // Given - configuration already set in setUp()

        // When
        OcrService service = new OcrService(ocrConfig);

        // Then
        assertNotNull(service);
    }

    @Test
    void testServiceInitializationWhenDisabled() {
        // Given
        when(ocrConfig.isEnabled()).thenReturn(false);

        // When
        OcrService service = new OcrService(ocrConfig);

        // Then
        assertNotNull(service);
        assertFalse(service.isAvailable());
    }

    @Test
    void testRecognizeTextWithNullFile() {
        // When
        OcrResult result = ocrService.recognizeText((File) null);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(OcrResult.Status.FAILED, result.getStatus());
        assertTrue(result.getErrorMessage().contains("does not exist"));
    }

    @Test
    void testRecognizeTextWithNonExistentFile() {
        // Given
        File nonExistentFile = new File("non_existent_file.png");

        // When
        OcrResult result = ocrService.recognizeText(nonExistentFile);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(OcrResult.Status.FAILED, result.getStatus());
        assertTrue(result.getErrorMessage().contains("does not exist"));
    }

    @Test
    void testRecognizeTextWithNullBufferedImage() {
        // When
        OcrResult result = ocrService.recognizeText((BufferedImage) null);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(OcrResult.Status.FAILED, result.getStatus());
        assertTrue(result.getErrorMessage().contains("null"));
    }

    @Test
    void testRecognizeTextWhenServiceDisabled() {
        // Given
        when(ocrConfig.isEnabled()).thenReturn(false);
        OcrService disabledService = new OcrService(ocrConfig);
        BufferedImage testImage = new BufferedImage(100, 100, BufferedImage.TYPE_INT_RGB);

        // When
        OcrResult result = disabledService.recognizeText(testImage);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(OcrResult.Status.FAILED, result.getStatus());
        assertTrue(result.getErrorMessage().contains("disabled"));
    }

    @Test
    void testRecognizeTextAsyncWithNullFile() {
        // When
        CompletableFuture<OcrResult> future = ocrService.recognizeTextAsync((File) null);

        // Then
        assertNotNull(future);
        OcrResult result = future.join();
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(OcrResult.Status.FAILED, result.getStatus());
    }

    @Test
    void testRecognizeTextAsyncWithNullBufferedImage() {
        // When
        CompletableFuture<OcrResult> future = ocrService.recognizeTextAsync((BufferedImage) null);

        // Then
        assertNotNull(future);
        OcrResult result = future.join();
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(OcrResult.Status.FAILED, result.getStatus());
    }

    @Test
    void testIsAvailableWhenEnabled() {
        // Given - configuration already set in setUp()

        // When & Then
        // Note: This test may fail if Tesseract is not properly installed
        // In a real environment, we would mock the Tesseract initialization
        try {
            ocrService.initialize();
            assertTrue(ocrService.isAvailable());
        } catch (RuntimeException e) {
            // Expected if Tesseract is not installed
            assertFalse(ocrService.isAvailable());
        }
    }

    @Test
    void testIsAvailableWhenDisabled() {
        // Given
        when(ocrConfig.isEnabled()).thenReturn(false);
        OcrService disabledService = new OcrService(ocrConfig);

        // When & Then
        assertFalse(disabledService.isAvailable());
    }

    @Test
    void testGetConfigInfo() {
        // Given - configuration already set in setUp()

        // When
        var configInfo = ocrService.getConfigInfo();

        // Then
        assertNotNull(configInfo);
        assertTrue(configInfo.containsKey("languages"));
        assertTrue(configInfo.containsKey("pageSegmentationMode"));
        assertTrue(configInfo.containsKey("ocrEngineMode"));
        assertTrue(configInfo.containsKey("confidenceThreshold"));
        assertTrue(configInfo.containsKey("preprocessingEnabled"));
        assertTrue(configInfo.containsKey("timeoutSeconds"));

        assertEquals(List.of("eng", "chi_sim"), configInfo.get("languages"));
        assertEquals(3, configInfo.get("pageSegmentationMode"));
        assertEquals(3, configInfo.get("ocrEngineMode"));
        assertEquals(60, configInfo.get("confidenceThreshold"));
        assertEquals(true, configInfo.get("preprocessingEnabled"));
        assertEquals(30, configInfo.get("timeoutSeconds"));
    }

    @Test
    void testShutdown() {
        // When & Then
        assertDoesNotThrow(() -> ocrService.shutdown());
    }

    @Test
    void testRecognizeTextWithValidImageShouldHandleException() {
        // Given
        BufferedImage testImage = new BufferedImage(100, 100, BufferedImage.TYPE_INT_RGB);

        // When
        OcrResult result = ocrService.recognizeText(testImage);

        // Then
        assertNotNull(result);
        // The result will likely be a failure due to Tesseract not being properly initialized in test environment
        // This is expected behavior for unit tests
    }

    @Test
    void testConfigurationValidation() {
        // Test that the service properly uses configuration values
        // Force some method calls to verify configuration usage
        ocrService.getConfigInfo();

        // Verify the methods that are actually called by getConfigInfo()
        verify(ocrConfig, atLeastOnce()).getLanguages();
        verify(ocrConfig, atLeastOnce()).getPageSegmentationMode();
        verify(ocrConfig, atLeastOnce()).getOcrEngineMode();
        verify(ocrConfig, atLeastOnce()).getConfidenceThreshold();
        verify(ocrConfig, atLeastOnce()).isPreprocessingEnabled();
        verify(ocrConfig, atLeastOnce()).getTimeoutSeconds();
    }
}
