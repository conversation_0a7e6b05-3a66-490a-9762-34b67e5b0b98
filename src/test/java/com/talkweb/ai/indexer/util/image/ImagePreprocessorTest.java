package com.talkweb.ai.indexer.util.image;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import javax.imageio.ImageIO;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 图像预处理器测试类
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
class ImagePreprocessorTest {
    
    private ImagePreprocessor imagePreprocessor;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        imagePreprocessor = new ImagePreprocessor();
    }
    
    @Test
    void testIsSupportedFormat() throws IOException {
        // Create actual test files for supported formats
        File pngFile = tempDir.resolve("test.png").toFile();
        File jpgFile = tempDir.resolve("test.jpg").toFile();
        File jpegFile = tempDir.resolve("test.jpeg").toFile();
        File tiffFile = tempDir.resolve("test.tiff").toFile();
        File bmpFile = tempDir.resolve("test.bmp").toFile();
        File gifFile = tempDir.resolve("test.gif").toFile();

        // Create the files
        pngFile.createNewFile();
        jpgFile.createNewFile();
        jpegFile.createNewFile();
        tiffFile.createNewFile();
        bmpFile.createNewFile();
        gifFile.createNewFile();

        // Test supported formats
        assertTrue(imagePreprocessor.isSupportedFormat(pngFile));
        assertTrue(imagePreprocessor.isSupportedFormat(jpgFile));
        assertTrue(imagePreprocessor.isSupportedFormat(jpegFile));
        assertTrue(imagePreprocessor.isSupportedFormat(tiffFile));
        assertTrue(imagePreprocessor.isSupportedFormat(bmpFile));
        assertTrue(imagePreprocessor.isSupportedFormat(gifFile));

        // Test case insensitive
        File pngUpperFile = tempDir.resolve("test.PNG").toFile();
        File jpgUpperFile = tempDir.resolve("test.JPG").toFile();
        pngUpperFile.createNewFile();
        jpgUpperFile.createNewFile();
        assertTrue(imagePreprocessor.isSupportedFormat(pngUpperFile));
        assertTrue(imagePreprocessor.isSupportedFormat(jpgUpperFile));

        // Test unsupported formats
        File pdfFile = tempDir.resolve("test.pdf").toFile();
        File txtFile = tempDir.resolve("test.txt").toFile();
        File noExtFile = tempDir.resolve("test").toFile();
        pdfFile.createNewFile();
        txtFile.createNewFile();
        noExtFile.createNewFile();

        assertFalse(imagePreprocessor.isSupportedFormat(pdfFile));
        assertFalse(imagePreprocessor.isSupportedFormat(txtFile));
        assertFalse(imagePreprocessor.isSupportedFormat(noExtFile));

        // Test null and non-existent files
        assertFalse(imagePreprocessor.isSupportedFormat(null));
        assertFalse(imagePreprocessor.isSupportedFormat(new File("non_existent.png")));
    }
    
    @Test
    void testGetImageFormat() {
        assertEquals("png", imagePreprocessor.getImageFormat(new File("test.png")));
        assertEquals("jpg", imagePreprocessor.getImageFormat(new File("test.jpg")));
        assertEquals("jpg", imagePreprocessor.getImageFormat(new File("test.jpeg")));
        assertEquals("tiff", imagePreprocessor.getImageFormat(new File("test.tiff")));
        assertEquals("bmp", imagePreprocessor.getImageFormat(new File("test.bmp")));
        assertEquals("gif", imagePreprocessor.getImageFormat(new File("test.gif")));
        
        // Test case insensitive
        assertEquals("png", imagePreprocessor.getImageFormat(new File("test.PNG")));
        
        // Test unsupported formats
        assertNull(imagePreprocessor.getImageFormat(new File("test.pdf")));
        assertNull(imagePreprocessor.getImageFormat(null));
    }
    
    @Test
    void testPreprocessImageWithNullFile() {
        ImagePreprocessor.PreprocessingResult result = 
            imagePreprocessor.preprocessImage((File) null, new ImagePreprocessor.PreprocessingOptions());
        
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getErrorMessage().contains("does not exist"));
    }
    
    @Test
    void testPreprocessImageWithNonExistentFile() {
        File nonExistentFile = new File("non_existent.png");
        ImagePreprocessor.PreprocessingResult result = 
            imagePreprocessor.preprocessImage(nonExistentFile, new ImagePreprocessor.PreprocessingOptions());
        
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getErrorMessage().contains("does not exist"));
    }
    
    @Test
    void testPreprocessImageWithNullBufferedImage() {
        ImagePreprocessor.PreprocessingResult result = 
            imagePreprocessor.preprocessImage((BufferedImage) null, new ImagePreprocessor.PreprocessingOptions());
        
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getErrorMessage().contains("null"));
    }
    
    @Test
    void testPreprocessImageWithValidImage() {
        // Create a test image
        BufferedImage testImage = createTestImage(200, 200);
        ImagePreprocessor.PreprocessingOptions options = new ImagePreprocessor.PreprocessingOptions();
        
        // When
        ImagePreprocessor.PreprocessingResult result = 
            imagePreprocessor.preprocessImage(testImage, options);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getProcessedImage());
        assertNotNull(result.getProcessingInfo());
        assertNull(result.getErrorMessage());
        
        // Check processing info
        assertTrue(result.getProcessingInfo().containsKey("originalWidth"));
        assertTrue(result.getProcessingInfo().containsKey("originalHeight"));
        assertEquals(200, result.getProcessingInfo().get("originalWidth"));
        assertEquals(200, result.getProcessingInfo().get("originalHeight"));
    }
    
    @Test
    void testPreprocessImageWithNullOptions() {
        BufferedImage testImage = createTestImage(100, 100);
        
        ImagePreprocessor.PreprocessingResult result = 
            imagePreprocessor.preprocessImage(testImage, null);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getProcessedImage());
    }
    
    @Test
    void testPreprocessingOptionsDefaults() {
        ImagePreprocessor.PreprocessingOptions options = new ImagePreprocessor.PreprocessingOptions();
        
        assertTrue(options.isEnableDenoising());
        assertTrue(options.isEnableBinarization());
        assertTrue(options.isEnableSkewCorrection());
        assertTrue(options.isEnableResolutionOptimization());
        assertTrue(options.isEnableContrastEnhancement());
        assertEquals(300, options.getTargetDpi());
        assertEquals(1.2, options.getContrastFactor(), 0.01);
        assertEquals(1.1, options.getBrightnessFactor(), 0.01);
        assertEquals(128, options.getBinarizationThreshold());
    }
    
    @Test
    void testPreprocessingOptionsValidation() {
        ImagePreprocessor.PreprocessingOptions options = new ImagePreprocessor.PreprocessingOptions();
        
        // Test DPI bounds
        options.setTargetDpi(50);
        assertEquals(72, options.getTargetDpi()); // Should be clamped to minimum
        
        options.setTargetDpi(700);
        assertEquals(600, options.getTargetDpi()); // Should be clamped to maximum
        
        // Test contrast factor bounds
        options.setContrastFactor(0.1);
        assertEquals(0.5, options.getContrastFactor(), 0.01); // Should be clamped to minimum
        
        options.setContrastFactor(5.0);
        assertEquals(3.0, options.getContrastFactor(), 0.01); // Should be clamped to maximum
        
        // Test brightness factor bounds
        options.setBrightnessFactor(0.1);
        assertEquals(0.5, options.getBrightnessFactor(), 0.01); // Should be clamped to minimum
        
        options.setBrightnessFactor(5.0);
        assertEquals(3.0, options.getBrightnessFactor(), 0.01); // Should be clamped to maximum
        
        // Test binarization threshold bounds
        options.setBinarizationThreshold(-10);
        assertEquals(0, options.getBinarizationThreshold()); // Should be clamped to minimum
        
        options.setBinarizationThreshold(300);
        assertEquals(255, options.getBinarizationThreshold()); // Should be clamped to maximum
    }
    
    @Test
    void testPreprocessImageWithAllOptionsDisabled() {
        BufferedImage testImage = createTestImage(100, 100);
        ImagePreprocessor.PreprocessingOptions options = new ImagePreprocessor.PreprocessingOptions();
        
        // Disable all preprocessing
        options.setEnableDenoising(false);
        options.setEnableBinarization(false);
        options.setEnableSkewCorrection(false);
        options.setEnableResolutionOptimization(false);
        options.setEnableContrastEnhancement(false);
        
        ImagePreprocessor.PreprocessingResult result = 
            imagePreprocessor.preprocessImage(testImage, options);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getProcessedImage());
        
        // The processed image should be similar to the original since no processing was applied
        assertEquals(testImage.getWidth(), result.getProcessedImage().getWidth());
        assertEquals(testImage.getHeight(), result.getProcessedImage().getHeight());
    }
    
    @Test
    void testPreprocessImageWithFileInput() throws IOException {
        // Create a test image file
        BufferedImage testImage = createTestImage(150, 150);
        File imageFile = tempDir.resolve("test.png").toFile();
        ImageIO.write(testImage, "png", imageFile);
        
        ImagePreprocessor.PreprocessingOptions options = new ImagePreprocessor.PreprocessingOptions();
        
        ImagePreprocessor.PreprocessingResult result = 
            imagePreprocessor.preprocessImage(imageFile, options);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getProcessedImage());
        assertNotNull(result.getProcessingInfo());
    }
    
    @Test
    void testPreprocessingResultSuccess() {
        BufferedImage testImage = createTestImage(100, 100);
        java.util.Map<String, Object> processingInfo = new java.util.HashMap<>();
        processingInfo.put("test", "value");
        
        ImagePreprocessor.PreprocessingResult result = 
            new ImagePreprocessor.PreprocessingResult(testImage, processingInfo);
        
        assertTrue(result.isSuccess());
        assertEquals(testImage, result.getProcessedImage());
        assertEquals(processingInfo, result.getProcessingInfo());
        assertNull(result.getErrorMessage());
    }
    
    @Test
    void testPreprocessingResultFailure() {
        String errorMessage = "Test error";
        
        ImagePreprocessor.PreprocessingResult result = 
            new ImagePreprocessor.PreprocessingResult(errorMessage);
        
        assertFalse(result.isSuccess());
        assertNull(result.getProcessedImage());
        assertNotNull(result.getProcessingInfo());
        assertEquals(errorMessage, result.getErrorMessage());
    }
    
    // Helper method to create test images
    private BufferedImage createTestImage(int width, int height) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // Fill with white background
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);
        
        // Add some black text
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("Arial", Font.PLAIN, 12));
        g2d.drawString("Test Text", 10, 20);
        g2d.drawString("Sample Content", 10, 40);
        
        // Add some shapes
        g2d.drawRect(10, 50, 50, 30);
        g2d.fillOval(70, 50, 20, 20);
        
        g2d.dispose();
        return image;
    }
}
