package com.talkweb.ai.indexer.config;

import com.talkweb.ai.indexer.util.image.ImagePreprocessor;

import java.util.Objects;

/**
 * 图像转换选项配置类
 * 
 * 提供图像到Markdown转换过程中的各种配置选项，
 * 包括OCR设置、预处理参数、输出格式等。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class ImageConversionOptions {
    
    /**
     * 输出格式枚举
     */
    public enum OutputFormat {
        MARKDOWN,           // 纯Markdown格式
        MARKDOWN_WITH_META, // 包含元数据的Markdown
        STRUCTURED_TEXT     // 结构化文本
    }
    
    /**
     * 文本结构化级别
     */
    public enum StructureLevel {
        NONE,      // 不进行结构化
        BASIC,     // 基本结构化（段落分离）
        ADVANCED   // 高级结构化（标题、列表等）
    }
    
    // OCR相关选项
    private boolean ocrEnabled = true;
    private boolean preprocessingEnabled = true;
    private int confidenceThreshold = 60;
    private boolean includeConfidenceInfo = false;
    private boolean includeBoundingBoxes = false;
    
    // 预处理选项
    private ImagePreprocessor.PreprocessingOptions preprocessingOptions;
    
    // 输出格式选项
    private OutputFormat outputFormat = OutputFormat.MARKDOWN;
    private StructureLevel structureLevel = StructureLevel.BASIC;
    private boolean includeMetadata = true;
    private boolean includeProcessingInfo = false;
    
    // 文本处理选项
    private boolean removeExtraWhitespace = true;
    private boolean normalizeLineBreaks = true;
    private boolean trimText = true;
    private int maxLineLength = 0; // 0表示不限制
    
    // 质量控制选项
    private boolean skipLowConfidenceText = false;
    private int minTextLength = 1;
    private boolean validateTextContent = true;
    
    // 性能选项
    private boolean enableAsyncProcessing = true;
    private int timeoutSeconds = 30;
    private boolean enableCaching = true;
    
    // 调试选项
    private boolean verboseLogging = false;
    private boolean savePreprocessedImage = false;
    private String debugOutputDir;
    
    // Constructors
    public ImageConversionOptions() {
        this.preprocessingOptions = new ImagePreprocessor.PreprocessingOptions();
    }
    
    public ImageConversionOptions(ImageConversionOptions other) {
        if (other != null) {
            this.ocrEnabled = other.ocrEnabled;
            this.preprocessingEnabled = other.preprocessingEnabled;
            this.confidenceThreshold = other.confidenceThreshold;
            this.includeConfidenceInfo = other.includeConfidenceInfo;
            this.includeBoundingBoxes = other.includeBoundingBoxes;
            this.outputFormat = other.outputFormat;
            this.structureLevel = other.structureLevel;
            this.includeMetadata = other.includeMetadata;
            this.includeProcessingInfo = other.includeProcessingInfo;
            this.removeExtraWhitespace = other.removeExtraWhitespace;
            this.normalizeLineBreaks = other.normalizeLineBreaks;
            this.trimText = other.trimText;
            this.maxLineLength = other.maxLineLength;
            this.skipLowConfidenceText = other.skipLowConfidenceText;
            this.minTextLength = other.minTextLength;
            this.validateTextContent = other.validateTextContent;
            this.enableAsyncProcessing = other.enableAsyncProcessing;
            this.timeoutSeconds = other.timeoutSeconds;
            this.enableCaching = other.enableCaching;
            this.verboseLogging = other.verboseLogging;
            this.savePreprocessedImage = other.savePreprocessedImage;
            this.debugOutputDir = other.debugOutputDir;
            
            // 深拷贝预处理选项
            this.preprocessingOptions = new ImagePreprocessor.PreprocessingOptions();
            if (other.preprocessingOptions != null) {
                copyPreprocessingOptions(other.preprocessingOptions);
            }
        } else {
            this.preprocessingOptions = new ImagePreprocessor.PreprocessingOptions();
        }
    }
    
    // Factory methods
    public static ImageConversionOptions createDefault() {
        return new ImageConversionOptions();
    }
    
    public static ImageConversionOptions createHighQuality() {
        ImageConversionOptions options = new ImageConversionOptions();
        options.setConfidenceThreshold(70);
        options.setStructureLevel(StructureLevel.ADVANCED);
        options.setIncludeMetadata(true);
        options.setSkipLowConfidenceText(true);
        options.setValidateTextContent(true);
        
        // 高质量预处理设置
        ImagePreprocessor.PreprocessingOptions preprocessing = options.getPreprocessingOptions();
        preprocessing.setTargetDpi(300);
        preprocessing.setContrastFactor(1.3);
        preprocessing.setBrightnessFactor(1.2);
        
        return options;
    }
    
    public static ImageConversionOptions createFast() {
        ImageConversionOptions options = new ImageConversionOptions();
        options.setPreprocessingEnabled(false);
        options.setStructureLevel(StructureLevel.NONE);
        options.setIncludeMetadata(false);
        options.setValidateTextContent(false);
        options.setTimeoutSeconds(10);
        
        return options;
    }
    
    // Getters and Setters
    public boolean isOcrEnabled() {
        return ocrEnabled;
    }
    
    public void setOcrEnabled(boolean ocrEnabled) {
        this.ocrEnabled = ocrEnabled;
    }
    
    public boolean isPreprocessingEnabled() {
        return preprocessingEnabled;
    }
    
    public void setPreprocessingEnabled(boolean preprocessingEnabled) {
        this.preprocessingEnabled = preprocessingEnabled;
    }
    
    public int getConfidenceThreshold() {
        return confidenceThreshold;
    }
    
    public void setConfidenceThreshold(int confidenceThreshold) {
        this.confidenceThreshold = Math.max(0, Math.min(100, confidenceThreshold));
    }
    
    public boolean isIncludeConfidenceInfo() {
        return includeConfidenceInfo;
    }
    
    public void setIncludeConfidenceInfo(boolean includeConfidenceInfo) {
        this.includeConfidenceInfo = includeConfidenceInfo;
    }
    
    public boolean isIncludeBoundingBoxes() {
        return includeBoundingBoxes;
    }
    
    public void setIncludeBoundingBoxes(boolean includeBoundingBoxes) {
        this.includeBoundingBoxes = includeBoundingBoxes;
    }
    
    public ImagePreprocessor.PreprocessingOptions getPreprocessingOptions() {
        return preprocessingOptions;
    }
    
    public void setPreprocessingOptions(ImagePreprocessor.PreprocessingOptions preprocessingOptions) {
        this.preprocessingOptions = preprocessingOptions != null ? 
            preprocessingOptions : new ImagePreprocessor.PreprocessingOptions();
    }
    
    public OutputFormat getOutputFormat() {
        return outputFormat;
    }
    
    public void setOutputFormat(OutputFormat outputFormat) {
        this.outputFormat = outputFormat != null ? outputFormat : OutputFormat.MARKDOWN;
    }
    
    public StructureLevel getStructureLevel() {
        return structureLevel;
    }
    
    public void setStructureLevel(StructureLevel structureLevel) {
        this.structureLevel = structureLevel != null ? structureLevel : StructureLevel.BASIC;
    }
    
    public boolean isIncludeMetadata() {
        return includeMetadata;
    }
    
    public void setIncludeMetadata(boolean includeMetadata) {
        this.includeMetadata = includeMetadata;
    }
    
    public boolean isIncludeProcessingInfo() {
        return includeProcessingInfo;
    }
    
    public void setIncludeProcessingInfo(boolean includeProcessingInfo) {
        this.includeProcessingInfo = includeProcessingInfo;
    }
    
    public boolean isRemoveExtraWhitespace() {
        return removeExtraWhitespace;
    }
    
    public void setRemoveExtraWhitespace(boolean removeExtraWhitespace) {
        this.removeExtraWhitespace = removeExtraWhitespace;
    }
    
    public boolean isNormalizeLineBreaks() {
        return normalizeLineBreaks;
    }
    
    public void setNormalizeLineBreaks(boolean normalizeLineBreaks) {
        this.normalizeLineBreaks = normalizeLineBreaks;
    }
    
    public boolean isTrimText() {
        return trimText;
    }
    
    public void setTrimText(boolean trimText) {
        this.trimText = trimText;
    }
    
    public int getMaxLineLength() {
        return maxLineLength;
    }
    
    public void setMaxLineLength(int maxLineLength) {
        this.maxLineLength = Math.max(0, maxLineLength);
    }
    
    public boolean isSkipLowConfidenceText() {
        return skipLowConfidenceText;
    }
    
    public void setSkipLowConfidenceText(boolean skipLowConfidenceText) {
        this.skipLowConfidenceText = skipLowConfidenceText;
    }
    
    public int getMinTextLength() {
        return minTextLength;
    }
    
    public void setMinTextLength(int minTextLength) {
        this.minTextLength = Math.max(0, minTextLength);
    }
    
    public boolean isValidateTextContent() {
        return validateTextContent;
    }
    
    public void setValidateTextContent(boolean validateTextContent) {
        this.validateTextContent = validateTextContent;
    }
    
    public boolean isEnableAsyncProcessing() {
        return enableAsyncProcessing;
    }
    
    public void setEnableAsyncProcessing(boolean enableAsyncProcessing) {
        this.enableAsyncProcessing = enableAsyncProcessing;
    }
    
    public int getTimeoutSeconds() {
        return timeoutSeconds;
    }
    
    public void setTimeoutSeconds(int timeoutSeconds) {
        this.timeoutSeconds = Math.max(1, timeoutSeconds);
    }
    
    public boolean isEnableCaching() {
        return enableCaching;
    }
    
    public void setEnableCaching(boolean enableCaching) {
        this.enableCaching = enableCaching;
    }
    
    public boolean isVerboseLogging() {
        return verboseLogging;
    }
    
    public void setVerboseLogging(boolean verboseLogging) {
        this.verboseLogging = verboseLogging;
    }
    
    public boolean isSavePreprocessedImage() {
        return savePreprocessedImage;
    }
    
    public void setSavePreprocessedImage(boolean savePreprocessedImage) {
        this.savePreprocessedImage = savePreprocessedImage;
    }
    
    public String getDebugOutputDir() {
        return debugOutputDir;
    }
    
    public void setDebugOutputDir(String debugOutputDir) {
        this.debugOutputDir = debugOutputDir;
    }
    
    // Utility methods
    
    /**
     * 验证配置的有效性
     */
    public boolean isValid() {
        return confidenceThreshold >= 0 && confidenceThreshold <= 100 &&
               timeoutSeconds > 0 &&
               minTextLength >= 0 &&
               maxLineLength >= 0;
    }
    
    /**
     * 创建配置的副本
     */
    public ImageConversionOptions copy() {
        return new ImageConversionOptions(this);
    }
    
    // Private helper methods
    
    private void copyPreprocessingOptions(ImagePreprocessor.PreprocessingOptions source) {
        if (source != null && preprocessingOptions != null) {
            preprocessingOptions.setEnableDenoising(source.isEnableDenoising());
            preprocessingOptions.setEnableBinarization(source.isEnableBinarization());
            preprocessingOptions.setEnableSkewCorrection(source.isEnableSkewCorrection());
            preprocessingOptions.setEnableResolutionOptimization(source.isEnableResolutionOptimization());
            preprocessingOptions.setEnableContrastEnhancement(source.isEnableContrastEnhancement());
            preprocessingOptions.setTargetDpi(source.getTargetDpi());
            preprocessingOptions.setContrastFactor(source.getContrastFactor());
            preprocessingOptions.setBrightnessFactor(source.getBrightnessFactor());
            preprocessingOptions.setBinarizationThreshold(source.getBinarizationThreshold());
        }
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ImageConversionOptions that = (ImageConversionOptions) o;
        return ocrEnabled == that.ocrEnabled &&
               preprocessingEnabled == that.preprocessingEnabled &&
               confidenceThreshold == that.confidenceThreshold &&
               includeConfidenceInfo == that.includeConfidenceInfo &&
               includeBoundingBoxes == that.includeBoundingBoxes &&
               includeMetadata == that.includeMetadata &&
               includeProcessingInfo == that.includeProcessingInfo &&
               removeExtraWhitespace == that.removeExtraWhitespace &&
               normalizeLineBreaks == that.normalizeLineBreaks &&
               trimText == that.trimText &&
               maxLineLength == that.maxLineLength &&
               skipLowConfidenceText == that.skipLowConfidenceText &&
               minTextLength == that.minTextLength &&
               validateTextContent == that.validateTextContent &&
               enableAsyncProcessing == that.enableAsyncProcessing &&
               timeoutSeconds == that.timeoutSeconds &&
               enableCaching == that.enableCaching &&
               verboseLogging == that.verboseLogging &&
               savePreprocessedImage == that.savePreprocessedImage &&
               outputFormat == that.outputFormat &&
               structureLevel == that.structureLevel &&
               Objects.equals(debugOutputDir, that.debugOutputDir);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(ocrEnabled, preprocessingEnabled, confidenceThreshold, 
                          includeConfidenceInfo, includeBoundingBoxes, outputFormat, 
                          structureLevel, includeMetadata, includeProcessingInfo, 
                          removeExtraWhitespace, normalizeLineBreaks, trimText, 
                          maxLineLength, skipLowConfidenceText, minTextLength, 
                          validateTextContent, enableAsyncProcessing, timeoutSeconds, 
                          enableCaching, verboseLogging, savePreprocessedImage, 
                          debugOutputDir);
    }
    
    @Override
    public String toString() {
        return "ImageConversionOptions{" +
               "ocrEnabled=" + ocrEnabled +
               ", preprocessingEnabled=" + preprocessingEnabled +
               ", confidenceThreshold=" + confidenceThreshold +
               ", outputFormat=" + outputFormat +
               ", structureLevel=" + structureLevel +
               ", includeMetadata=" + includeMetadata +
               ", timeoutSeconds=" + timeoutSeconds +
               '}';
    }
}
