package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.config.ImageConversionOptions;
import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.converter.AbstractDocumentConverter;
import com.talkweb.ai.indexer.core.converter.ConversionCapabilities;
import com.talkweb.ai.indexer.core.converter.ConversionContext;
import com.talkweb.ai.indexer.core.converter.ConversionMetadata;
import com.talkweb.ai.indexer.model.OcrResult;
import com.talkweb.ai.indexer.service.OcrService;
import com.talkweb.ai.indexer.util.image.ImagePreprocessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 图像到Markdown转换器
 *
 * 支持多种图像格式的OCR识别和Markdown转换，包括图像预处理、
 * 文本识别、结构化处理和质量控制等功能。
 *
 * 支持的格式：PNG, JPG, JPEG, TIFF, BMP, GIF
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class ImageToMarkdownConverter extends AbstractDocumentConverter {

    private static final Logger logger = LoggerFactory.getLogger(ImageToMarkdownConverter.class);

    // 支持的文件扩展名
    private static final Set<String> SUPPORTED_EXTENSIONS = Set.of(
        "png", "jpg", "jpeg", "tiff", "tif", "bmp", "gif"
    );

    private final OcrService ocrService;
    private final ImagePreprocessor imagePreprocessor;
    private ImageConversionOptions defaultOptions;

    @Autowired
    public ImageToMarkdownConverter(OcrService ocrService, ImagePreprocessor imagePreprocessor) {
        super();
        this.ocrService = ocrService;
        this.imagePreprocessor = imagePreprocessor;
        this.defaultOptions = ImageConversionOptions.createDefault();
    }

    @Override
    protected ConversionMetadata createMetadata() {
        return ConversionMetadata.builder("Image to Markdown Converter")
                .description("Converts image files to Markdown format using OCR technology")
                .version("1.0")
                .attribute("author", "AI Assistant")
                .attribute("supportedInputFormats", SUPPORTED_EXTENSIONS)
                .attribute("supportedOutputFormats", Set.of("md"))
                .attribute("ocrEnabled", true)
                .attribute("preprocessingEnabled", true)
                .build();
    }

    @Override
    public Set<String> getSupportedExtensions() {
        return SUPPORTED_EXTENSIONS;
    }

    @Override
    public ConversionCapabilities getCapabilities() {
        return ConversionCapabilities.builder()
                .feature(ConversionCapabilities.Features.METADATA)
                .feature(ConversionCapabilities.Features.IMAGES)
                .capability("ocrSupport", true)
                .capability("multiLanguage", true)
                .capability("imagePreprocessing", true)
                .capability("confidenceScoring", true)
                .capability("asyncProcessing", true)
                .capability("batchProcessing", true)
                .capability("maxFileSize", 50 * 1024 * 1024) // 50MB
                .build();
    }

    @Override
    protected ConversionResult doConvert(File inputFile, ConversionContext context) throws ConversionException {
        if (inputFile == null) {
            throw new ConversionException("Input file cannot be null");
        }

        logger.info("Starting image to Markdown conversion for: {}", inputFile.getName());

        // 验证文件
        if (!validateImageFile(inputFile)) {
            throw new ConversionException("Invalid image file: " + inputFile.getName());
        }

        // 获取转换选项
        ImageConversionOptions options = getConversionOptions(context);

        try {
            // 执行转换
            if (options.isEnableAsyncProcessing()) {
                return convertAsync(inputFile, options).get();
            } else {
                return convertSync(inputFile, options);
            }

        } catch (Exception e) {
            logger.error("Image conversion failed for file: {}", inputFile.getName(), e);
            throw new ConversionException("Image conversion failed: " + e.getMessage(), e);
        }
    }

    /**
     * 同步转换
     */
    private ConversionResult convertSync(File inputFile, ImageConversionOptions options) throws ConversionException {
        try {
            // 1. 读取图像
            BufferedImage originalImage = ImageIO.read(inputFile);
            if (originalImage == null) {
                throw new ConversionException("Failed to read image file: " + inputFile.getName());
            }

            // 2. 图像预处理
            BufferedImage processedImage = originalImage;
            ImagePreprocessor.PreprocessingResult preprocessingResult = null;

            if (options.isPreprocessingEnabled()) {
                preprocessingResult = imagePreprocessor.preprocessImage(originalImage, options.getPreprocessingOptions());
                if (preprocessingResult.isSuccess()) {
                    processedImage = preprocessingResult.getProcessedImage();
                    logger.debug("Image preprocessing completed successfully");
                } else {
                    logger.warn("Image preprocessing failed: {}, using original image", preprocessingResult.getErrorMessage());
                }
            }

            // 3. OCR识别
            OcrResult ocrResult;
            if (options.isOcrEnabled()) {
                ocrResult = ocrService.recognizeText(processedImage);
            } else {
                ocrResult = OcrResult.success("", 0.0f);
            }

            // 4. 生成Markdown
            String markdown = generateMarkdown(ocrResult, options, inputFile, preprocessingResult);

            // 5. 创建输出路径
            String parentDir = inputFile.getParent();
            if (parentDir == null) {
                parentDir = ".";
            }
            Path outputPath = Paths.get(parentDir, inputFile.getName() + ".md");

            // 6. 创建转换结果
            ConversionResult.Status status = determineStatus(ocrResult, options);
            ConversionResult result = new ConversionResult(
                status,
                inputFile.getPath(),
                outputPath.toString(),
                markdown
            );

            // 添加元数据
            addMetadataToResult(result, ocrResult, options, preprocessingResult);

            logger.info("Image conversion completed for: {}, status: {}, confidence: {}",
                       inputFile.getName(), status, ocrResult.getConfidence());

            return result;

        } catch (IOException e) {
            throw new ConversionException("Failed to read image file: " + inputFile.getName(), e);
        }
    }

    /**
     * 异步转换
     */
    private CompletableFuture<ConversionResult> convertAsync(File inputFile, ImageConversionOptions options) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return convertSync(inputFile, options);
            } catch (ConversionException e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 生成Markdown内容
     */
    private String generateMarkdown(OcrResult ocrResult, ImageConversionOptions options,
                                  File inputFile, ImagePreprocessor.PreprocessingResult preprocessingResult) {
        StringBuilder markdown = new StringBuilder();

        // 添加文档头部
        if (options.isIncludeMetadata()) {
            addDocumentHeader(markdown, inputFile, ocrResult);
        }

        // 处理OCR文本
        String text = ocrResult.getText();
        if (text != null && !text.trim().isEmpty()) {
            text = processText(text, options);

            // 根据结构化级别处理文本
            switch (options.getStructureLevel()) {
                case ADVANCED:
                    text = structureTextAdvanced(text);
                    break;
                case BASIC:
                    text = structureTextBasic(text);
                    break;
                case NONE:
                default:
                    // 保持原样
                    break;
            }

            markdown.append(text);
        } else {
            markdown.append("*No text content detected in image*\n\n");
        }

        // 添加处理信息
        if (options.isIncludeProcessingInfo()) {
            addProcessingInfo(markdown, ocrResult, preprocessingResult);
        }

        // 添加置信度信息
        if (options.isIncludeConfidenceInfo()) {
            addConfidenceInfo(markdown, ocrResult);
        }

        return markdown.toString();
    }

    /**
     * 添加文档头部
     */
    private void addDocumentHeader(StringBuilder markdown, File inputFile, OcrResult ocrResult) {
        markdown.append("# ").append(getFileNameWithoutExtension(inputFile.getName())).append("\n\n");
        markdown.append("**Source:** ").append(inputFile.getName()).append("\n");
        markdown.append("**Processed:** ").append(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)).append("\n");
        markdown.append("**OCR Confidence:** ").append(String.format("%.1f%%", ocrResult.getConfidence())).append("\n\n");
        markdown.append("---\n\n");
    }

    /**
     * 处理文本内容
     */
    private String processText(String text, ImageConversionOptions options) {
        if (text == null) {
            return "";
        }

        String processed = text;

        // 去除多余空白
        if (options.isRemoveExtraWhitespace()) {
            processed = processed.replaceAll("\\s+", " ");
        }

        // 标准化换行符
        if (options.isNormalizeLineBreaks()) {
            processed = processed.replaceAll("\\r\\n|\\r", "\n");
        }

        // 修剪文本
        if (options.isTrimText()) {
            processed = processed.trim();
        }

        // 限制行长度
        if (options.getMaxLineLength() > 0) {
            processed = wrapText(processed, options.getMaxLineLength());
        }

        return processed;
    }

    /**
     * 基本文本结构化
     */
    private String structureTextBasic(String text) {
        // 简单的段落分离
        return text.replaceAll("\\n\\s*\\n", "\n\n");
    }

    /**
     * 高级文本结构化
     */
    private String structureTextAdvanced(String text) {
        // 这里可以实现更复杂的结构化逻辑
        // 例如识别标题、列表等
        String structured = structureTextBasic(text);

        // 识别可能的标题（全大写或首字母大写的短行）
        structured = structured.replaceAll("(?m)^([A-Z][A-Z\\s]{2,20})$", "## $1");

        return structured;
    }

    /**
     * 文本换行
     */
    private String wrapText(String text, int maxLength) {
        StringBuilder wrapped = new StringBuilder();
        String[] lines = text.split("\n");

        for (String line : lines) {
            if (line.length() <= maxLength) {
                wrapped.append(line).append("\n");
            } else {
                String[] words = line.split(" ");
                StringBuilder currentLine = new StringBuilder();

                for (String word : words) {
                    if (currentLine.length() + word.length() + 1 <= maxLength) {
                        if (currentLine.length() > 0) {
                            currentLine.append(" ");
                        }
                        currentLine.append(word);
                    } else {
                        if (currentLine.length() > 0) {
                            wrapped.append(currentLine).append("\n");
                            currentLine = new StringBuilder(word);
                        } else {
                            wrapped.append(word).append("\n");
                        }
                    }
                }

                if (currentLine.length() > 0) {
                    wrapped.append(currentLine).append("\n");
                }
            }
        }

        return wrapped.toString();
    }

    /**
     * 添加处理信息
     */
    private void addProcessingInfo(StringBuilder markdown, OcrResult ocrResult,
                                 ImagePreprocessor.PreprocessingResult preprocessingResult) {
        markdown.append("\n---\n\n");
        markdown.append("## Processing Information\n\n");
        markdown.append("- **Processing Time:** ").append(ocrResult.getProcessingTimeMs()).append("ms\n");
        markdown.append("- **Text Length:** ").append(ocrResult.getTextLength()).append(" characters\n");
        markdown.append("- **Word Count:** ").append(ocrResult.getWordCount()).append("\n");

        if (preprocessingResult != null && preprocessingResult.isSuccess()) {
            markdown.append("- **Preprocessing:** Applied\n");
        }

        markdown.append("\n");
    }

    /**
     * 添加置信度信息
     */
    private void addConfidenceInfo(StringBuilder markdown, OcrResult ocrResult) {
        markdown.append("\n---\n\n");
        markdown.append("## OCR Quality Information\n\n");
        markdown.append("- **Overall Confidence:** ").append(String.format("%.1f%%", ocrResult.getConfidence())).append("\n");
        markdown.append("- **Status:** ").append(ocrResult.getStatus()).append("\n");

        if (ocrResult.getWords() != null && !ocrResult.getWords().isEmpty()) {
            float avgWordConfidence = (float) ocrResult.getWords().stream()
                .mapToDouble(OcrResult.WordResult::getConfidence)
                .average()
                .orElse(0.0);
            markdown.append("- **Average Word Confidence:** ").append(String.format("%.1f%%", avgWordConfidence)).append("\n");
        }

        markdown.append("\n");
    }

    // Helper methods

    private boolean validateImageFile(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return false;
        }

        return imagePreprocessor.isSupportedFormat(file);
    }

    private ImageConversionOptions getConversionOptions(ConversionContext context) {
        // 从上下文中获取选项，如果没有则使用默认选项
        ImageConversionOptions options = context.getOptions().getOption("imageConversionOptions", defaultOptions);
        return options != null ? options : defaultOptions;
    }

    private ConversionResult.Status determineStatus(OcrResult ocrResult, ImageConversionOptions options) {
        if (!ocrResult.isSuccess()) {
            return ConversionResult.Status.FAILED;
        }

        if (ocrResult.getConfidence() < options.getConfidenceThreshold()) {
            return ConversionResult.Status.PARTIAL_SUCCESS;
        }

        if (!ocrResult.hasText() || ocrResult.getTextLength() < options.getMinTextLength()) {
            return ConversionResult.Status.PARTIAL_SUCCESS;
        }

        return ConversionResult.Status.SUCCESS;
    }

    private void addMetadataToResult(ConversionResult result, OcrResult ocrResult,
                                   ImageConversionOptions options,
                                   ImagePreprocessor.PreprocessingResult preprocessingResult) {
        // ConversionResult doesn't support metadata addition in current implementation
        // Metadata information is already included in the markdown content when options.isIncludeProcessingInfo() is true
        // This method is kept for future extensibility when ConversionResult supports metadata
        logger.debug("Metadata would include: OCR Confidence: {}%, Status: {}, Text Length: {}, Word Count: {}, Processing Time: {}ms",
                    ocrResult.getConfidence(), ocrResult.getStatus(), ocrResult.getTextLength(),
                    ocrResult.getWordCount(), ocrResult.getProcessingTimeMs());
    }

    private String getFileNameWithoutExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
    }

    /**
     * 设置默认转换选项
     */
    public void setDefaultOptions(ImageConversionOptions options) {
        this.defaultOptions = options != null ? options : ImageConversionOptions.createDefault();
    }

    /**
     * 获取默认转换选项
     */
    public ImageConversionOptions getDefaultOptions() {
        return defaultOptions;
    }
}
