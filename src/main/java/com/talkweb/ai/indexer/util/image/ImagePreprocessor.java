package com.talkweb.ai.indexer.util.image;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.awt.image.ConvolveOp;
import java.awt.image.Kernel;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 图像预处理器
 * 
 * 提供图像预处理功能以提高OCR识别准确率，包括格式转换、
 * 去噪、二值化、倾斜校正、分辨率优化等功能。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class ImagePreprocessor {
    
    private static final Logger logger = LoggerFactory.getLogger(ImagePreprocessor.class);
    
    // 支持的图像格式
    private static final String[] SUPPORTED_FORMATS = {"png", "jpg", "jpeg", "tiff", "bmp", "gif"};
    
    // 默认处理参数
    private static final int DEFAULT_DPI = 300;
    private static final int MIN_WIDTH = 100;
    private static final int MIN_HEIGHT = 100;
    private static final int MAX_WIDTH = 4000;
    private static final int MAX_HEIGHT = 4000;
    
    /**
     * 图像预处理选项
     */
    public static class PreprocessingOptions {
        private boolean enableDenoising = true;
        private boolean enableBinarization = true;
        private boolean enableSkewCorrection = true;
        private boolean enableResolutionOptimization = true;
        private boolean enableContrastEnhancement = true;
        private int targetDpi = DEFAULT_DPI;
        private double contrastFactor = 1.2;
        private double brightnessFactor = 1.1;
        private int binarizationThreshold = 128; // 0-255
        
        // Getters and Setters
        public boolean isEnableDenoising() { return enableDenoising; }
        public void setEnableDenoising(boolean enableDenoising) { this.enableDenoising = enableDenoising; }
        
        public boolean isEnableBinarization() { return enableBinarization; }
        public void setEnableBinarization(boolean enableBinarization) { this.enableBinarization = enableBinarization; }
        
        public boolean isEnableSkewCorrection() { return enableSkewCorrection; }
        public void setEnableSkewCorrection(boolean enableSkewCorrection) { this.enableSkewCorrection = enableSkewCorrection; }
        
        public boolean isEnableResolutionOptimization() { return enableResolutionOptimization; }
        public void setEnableResolutionOptimization(boolean enableResolutionOptimization) { this.enableResolutionOptimization = enableResolutionOptimization; }
        
        public boolean isEnableContrastEnhancement() { return enableContrastEnhancement; }
        public void setEnableContrastEnhancement(boolean enableContrastEnhancement) { this.enableContrastEnhancement = enableContrastEnhancement; }
        
        public int getTargetDpi() { return targetDpi; }
        public void setTargetDpi(int targetDpi) { this.targetDpi = Math.max(72, Math.min(600, targetDpi)); }
        
        public double getContrastFactor() { return contrastFactor; }
        public void setContrastFactor(double contrastFactor) { this.contrastFactor = Math.max(0.5, Math.min(3.0, contrastFactor)); }
        
        public double getBrightnessFactor() { return brightnessFactor; }
        public void setBrightnessFactor(double brightnessFactor) { this.brightnessFactor = Math.max(0.5, Math.min(3.0, brightnessFactor)); }
        
        public int getBinarizationThreshold() { return binarizationThreshold; }
        public void setBinarizationThreshold(int binarizationThreshold) { this.binarizationThreshold = Math.max(0, Math.min(255, binarizationThreshold)); }
    }
    
    /**
     * 预处理结果
     */
    public static class PreprocessingResult {
        private BufferedImage processedImage;
        private Map<String, Object> processingInfo;
        private boolean success;
        private String errorMessage;
        
        public PreprocessingResult(BufferedImage processedImage, Map<String, Object> processingInfo) {
            this.processedImage = processedImage;
            this.processingInfo = processingInfo;
            this.success = true;
        }
        
        public PreprocessingResult(String errorMessage) {
            this.errorMessage = errorMessage;
            this.success = false;
            this.processingInfo = new HashMap<>();
        }
        
        // Getters
        public BufferedImage getProcessedImage() { return processedImage; }
        public Map<String, Object> getProcessingInfo() { return processingInfo; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
    }
    
    /**
     * 预处理图像文件
     * 
     * @param imageFile 输入图像文件
     * @param options 预处理选项
     * @return 预处理结果
     */
    public PreprocessingResult preprocessImage(File imageFile, PreprocessingOptions options) {
        if (imageFile == null || !imageFile.exists()) {
            return new PreprocessingResult("Image file does not exist");
        }
        
        try {
            logger.debug("Starting image preprocessing for file: {}", imageFile.getName());
            
            // 读取图像
            BufferedImage originalImage = ImageIO.read(imageFile);
            if (originalImage == null) {
                return new PreprocessingResult("Failed to read image file");
            }
            
            return preprocessImage(originalImage, options);
            
        } catch (IOException e) {
            logger.error("Failed to read image file: {}", imageFile.getName(), e);
            return new PreprocessingResult("Failed to read image: " + e.getMessage());
        }
    }
    
    /**
     * 预处理BufferedImage
     * 
     * @param originalImage 原始图像
     * @param options 预处理选项
     * @return 预处理结果
     */
    public PreprocessingResult preprocessImage(BufferedImage originalImage, PreprocessingOptions options) {
        if (originalImage == null) {
            return new PreprocessingResult("Original image is null");
        }
        
        if (options == null) {
            options = new PreprocessingOptions();
        }
        
        try {
            Map<String, Object> processingInfo = new HashMap<>();
            processingInfo.put("originalWidth", originalImage.getWidth());
            processingInfo.put("originalHeight", originalImage.getHeight());
            
            BufferedImage processedImage = originalImage;
            
            // 1. 分辨率优化
            if (options.isEnableResolutionOptimization()) {
                processedImage = optimizeResolution(processedImage, options);
                processingInfo.put("resolutionOptimized", true);
                processingInfo.put("newWidth", processedImage.getWidth());
                processingInfo.put("newHeight", processedImage.getHeight());
            }
            
            // 2. 对比度和亮度增强
            if (options.isEnableContrastEnhancement()) {
                processedImage = enhanceContrastAndBrightness(processedImage, options);
                processingInfo.put("contrastEnhanced", true);
                processingInfo.put("contrastFactor", options.getContrastFactor());
                processingInfo.put("brightnessFactor", options.getBrightnessFactor());
            }
            
            // 3. 去噪
            if (options.isEnableDenoising()) {
                processedImage = denoise(processedImage);
                processingInfo.put("denoised", true);
            }
            
            // 4. 倾斜校正
            if (options.isEnableSkewCorrection()) {
                double skewAngle = detectSkew(processedImage);
                if (Math.abs(skewAngle) > 0.5) { // 只有倾斜角度大于0.5度才校正
                    processedImage = correctSkew(processedImage, skewAngle);
                    processingInfo.put("skewCorrected", true);
                    processingInfo.put("skewAngle", skewAngle);
                }
            }
            
            // 5. 二值化
            if (options.isEnableBinarization()) {
                processedImage = binarize(processedImage, options.getBinarizationThreshold());
                processingInfo.put("binarized", true);
                processingInfo.put("binarizationThreshold", options.getBinarizationThreshold());
            }
            
            logger.debug("Image preprocessing completed successfully");
            return new PreprocessingResult(processedImage, processingInfo);
            
        } catch (Exception e) {
            logger.error("Image preprocessing failed", e);
            return new PreprocessingResult("Preprocessing failed: " + e.getMessage());
        }
    }
    
    /**
     * 检查图像格式是否支持
     * 
     * @param file 图像文件
     * @return 如果支持返回true
     */
    public boolean isSupportedFormat(File file) {
        if (file == null || !file.exists()) {
            return false;
        }
        
        String fileName = file.getName().toLowerCase();
        for (String format : SUPPORTED_FORMATS) {
            if (fileName.endsWith("." + format)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取图像格式
     * 
     * @param file 图像文件
     * @return 图像格式字符串
     */
    public String getImageFormat(File file) {
        if (file == null) {
            return null;
        }
        
        String fileName = file.getName().toLowerCase();
        for (String format : SUPPORTED_FORMATS) {
            if (fileName.endsWith("." + format)) {
                return format.equals("jpeg") ? "jpg" : format;
            }
        }
        return null;
    }
    
    // Private helper methods
    
    private BufferedImage optimizeResolution(BufferedImage image, PreprocessingOptions options) {
        int width = image.getWidth();
        int height = image.getHeight();
        
        // 计算目标尺寸
        double scaleFactor = calculateScaleFactor(width, height, options.getTargetDpi());
        
        if (Math.abs(scaleFactor - 1.0) < 0.1) {
            return image; // 不需要缩放
        }
        
        int newWidth = (int) (width * scaleFactor);
        int newHeight = (int) (height * scaleFactor);
        
        // 确保尺寸在合理范围内
        newWidth = Math.max(MIN_WIDTH, Math.min(MAX_WIDTH, newWidth));
        newHeight = Math.max(MIN_HEIGHT, Math.min(MAX_HEIGHT, newHeight));
        
        BufferedImage scaledImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = scaledImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        g2d.drawImage(image, 0, 0, newWidth, newHeight, null);
        g2d.dispose();
        
        return scaledImage;
    }
    
    private double calculateScaleFactor(int width, int height, int targetDpi) {
        // 假设原始图像是72 DPI，计算缩放因子
        double currentDpi = 72.0;
        return targetDpi / currentDpi;
    }
    
    private BufferedImage enhanceContrastAndBrightness(BufferedImage image, PreprocessingOptions options) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage enhancedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                Color originalColor = new Color(image.getRGB(x, y));
                
                // 应用亮度和对比度调整
                int red = adjustPixel(originalColor.getRed(), options.getBrightnessFactor(), options.getContrastFactor());
                int green = adjustPixel(originalColor.getGreen(), options.getBrightnessFactor(), options.getContrastFactor());
                int blue = adjustPixel(originalColor.getBlue(), options.getBrightnessFactor(), options.getContrastFactor());
                
                Color newColor = new Color(red, green, blue);
                enhancedImage.setRGB(x, y, newColor.getRGB());
            }
        }
        
        return enhancedImage;
    }
    
    private int adjustPixel(int pixel, double brightnessFactor, double contrastFactor) {
        // 应用亮度调整
        double adjusted = pixel * brightnessFactor;
        
        // 应用对比度调整
        adjusted = ((adjusted - 128) * contrastFactor) + 128;
        
        // 确保值在0-255范围内
        return Math.max(0, Math.min(255, (int) adjusted));
    }
    
    private BufferedImage denoise(BufferedImage image) {
        // 使用高斯模糊进行去噪
        float[] matrix = {
            0.0625f, 0.125f, 0.0625f,
            0.125f,  0.25f,  0.125f,
            0.0625f, 0.125f, 0.0625f
        };
        
        Kernel kernel = new Kernel(3, 3, matrix);
        ConvolveOp op = new ConvolveOp(kernel, ConvolveOp.EDGE_NO_OP, null);
        
        return op.filter(image, null);
    }
    
    private double detectSkew(BufferedImage image) {
        // 简化的倾斜检测算法
        // 在实际应用中，可以使用更复杂的算法如Hough变换
        // 这里返回一个模拟的倾斜角度
        return 0.0; // 暂时返回0，表示无倾斜
    }
    
    private BufferedImage correctSkew(BufferedImage image, double angle) {
        if (Math.abs(angle) < 0.1) {
            return image;
        }
        
        double radians = Math.toRadians(angle);
        
        // 计算旋转后的图像尺寸
        int width = image.getWidth();
        int height = image.getHeight();
        
        AffineTransform transform = new AffineTransform();
        transform.rotate(radians, width / 2.0, height / 2.0);
        
        BufferedImage rotatedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = rotatedImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);
        g2d.setTransform(transform);
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();
        
        return rotatedImage;
    }
    
    private BufferedImage binarize(BufferedImage image, int threshold) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage binaryImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_BINARY);
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                Color color = new Color(image.getRGB(x, y));
                int gray = (int) (0.299 * color.getRed() + 0.587 * color.getGreen() + 0.114 * color.getBlue());
                
                if (gray > threshold) {
                    binaryImage.setRGB(x, y, Color.WHITE.getRGB());
                } else {
                    binaryImage.setRGB(x, y, Color.BLACK.getRGB());
                }
            }
        }
        
        return binaryImage;
    }
}
