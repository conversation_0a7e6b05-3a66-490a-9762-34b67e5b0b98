package com.talkweb.ai.indexer.model;

import java.awt.Rectangle;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * OCR识别结果封装类
 * 
 * 包含OCR识别的文本内容、置信度、边界框信息、
 * 处理时间等详细信息，用于结果分析和质量评估。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class OcrResult {
    
    /**
     * OCR识别状态枚举
     */
    public enum Status {
        SUCCESS,        // 识别成功
        FAILED,         // 识别失败
        LOW_CONFIDENCE, // 低置信度
        TIMEOUT,        // 超时
        ERROR           // 错误
    }
    
    /**
     * 识别状态
     */
    private Status status;
    
    /**
     * 识别的文本内容
     */
    private String text;
    
    /**
     * 整体置信度 (0-100)
     */
    private float confidence;
    
    /**
     * 单词级别的识别结果
     */
    private List<WordResult> words;
    
    /**
     * 行级别的识别结果
     */
    private List<LineResult> lines;
    
    /**
     * 处理时间（毫秒）
     */
    private long processingTimeMs;
    
    /**
     * 识别时间
     */
    private LocalDateTime timestamp;
    
    /**
     * 错误信息（如果有）
     */
    private String errorMessage;
    
    /**
     * 使用的OCR配置信息
     */
    private Map<String, Object> ocrConfig;
    
    /**
     * 图像预处理信息
     */
    private Map<String, Object> preprocessingInfo;
    
    // Constructors
    public OcrResult() {
        this.timestamp = LocalDateTime.now();
    }
    
    public OcrResult(Status status, String text, float confidence) {
        this();
        this.status = status;
        this.text = text;
        this.confidence = confidence;
    }
    
    /**
     * 创建成功的OCR结果
     */
    public static OcrResult success(String text, float confidence) {
        return new OcrResult(Status.SUCCESS, text, confidence);
    }
    
    /**
     * 创建失败的OCR结果
     */
    public static OcrResult failure(String errorMessage) {
        OcrResult result = new OcrResult();
        result.status = Status.FAILED;
        result.errorMessage = errorMessage;
        result.text = "";
        result.confidence = 0.0f;
        return result;
    }
    
    /**
     * 创建低置信度的OCR结果
     */
    public static OcrResult lowConfidence(String text, float confidence) {
        return new OcrResult(Status.LOW_CONFIDENCE, text, confidence);
    }
    
    /**
     * 创建超时的OCR结果
     */
    public static OcrResult timeout() {
        OcrResult result = new OcrResult();
        result.status = Status.TIMEOUT;
        result.errorMessage = "OCR processing timeout";
        result.text = "";
        result.confidence = 0.0f;
        return result;
    }
    
    // Getters and Setters
    public Status getStatus() {
        return status;
    }
    
    public void setStatus(Status status) {
        this.status = status;
    }
    
    public String getText() {
        return text != null ? text : "";
    }
    
    public void setText(String text) {
        this.text = text;
    }
    
    public float getConfidence() {
        return confidence;
    }
    
    public void setConfidence(float confidence) {
        this.confidence = Math.max(0.0f, Math.min(100.0f, confidence));
    }
    
    public List<WordResult> getWords() {
        return words;
    }
    
    public void setWords(List<WordResult> words) {
        this.words = words;
    }
    
    public List<LineResult> getLines() {
        return lines;
    }
    
    public void setLines(List<LineResult> lines) {
        this.lines = lines;
    }
    
    public long getProcessingTimeMs() {
        return processingTimeMs;
    }
    
    public void setProcessingTimeMs(long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public Map<String, Object> getOcrConfig() {
        return ocrConfig;
    }
    
    public void setOcrConfig(Map<String, Object> ocrConfig) {
        this.ocrConfig = ocrConfig;
    }
    
    public Map<String, Object> getPreprocessingInfo() {
        return preprocessingInfo;
    }
    
    public void setPreprocessingInfo(Map<String, Object> preprocessingInfo) {
        this.preprocessingInfo = preprocessingInfo;
    }
    
    // Utility methods
    
    /**
     * 检查OCR结果是否成功
     */
    public boolean isSuccess() {
        return status == Status.SUCCESS;
    }
    
    /**
     * 检查是否有文本内容
     */
    public boolean hasText() {
        return text != null && !text.trim().isEmpty();
    }
    
    /**
     * 检查置信度是否达到阈值
     */
    public boolean isConfidenceAbove(float threshold) {
        return confidence >= threshold;
    }
    
    /**
     * 获取文本长度
     */
    public int getTextLength() {
        return text != null ? text.length() : 0;
    }
    
    /**
     * 获取单词数量
     */
    public int getWordCount() {
        if (words != null) {
            return words.size();
        }
        return text != null ? text.split("\\s+").length : 0;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OcrResult ocrResult = (OcrResult) o;
        return Float.compare(ocrResult.confidence, confidence) == 0 &&
               processingTimeMs == ocrResult.processingTimeMs &&
               status == ocrResult.status &&
               Objects.equals(text, ocrResult.text) &&
               Objects.equals(timestamp, ocrResult.timestamp);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(status, text, confidence, processingTimeMs, timestamp);
    }
    
    @Override
    public String toString() {
        return "OcrResult{" +
               "status=" + status +
               ", text='" + (text != null && text.length() > 50 ? text.substring(0, 50) + "..." : text) + '\'' +
               ", confidence=" + confidence +
               ", wordCount=" + getWordCount() +
               ", processingTimeMs=" + processingTimeMs +
               ", timestamp=" + timestamp +
               (errorMessage != null ? ", errorMessage='" + errorMessage + '\'' : "") +
               '}';
    }
    
    /**
     * 单词级别识别结果
     */
    public static class WordResult {
        private String text;
        private float confidence;
        private Rectangle boundingBox;
        
        public WordResult(String text, float confidence, Rectangle boundingBox) {
            this.text = text;
            this.confidence = confidence;
            this.boundingBox = boundingBox;
        }
        
        // Getters and Setters
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        
        public float getConfidence() { return confidence; }
        public void setConfidence(float confidence) { this.confidence = confidence; }
        
        public Rectangle getBoundingBox() { return boundingBox; }
        public void setBoundingBox(Rectangle boundingBox) { this.boundingBox = boundingBox; }
        
        @Override
        public String toString() {
            return "WordResult{text='" + text + "', confidence=" + confidence + ", boundingBox=" + boundingBox + '}';
        }
    }
    
    /**
     * 行级别识别结果
     */
    public static class LineResult {
        private String text;
        private float confidence;
        private Rectangle boundingBox;
        private List<WordResult> words;
        
        public LineResult(String text, float confidence, Rectangle boundingBox) {
            this.text = text;
            this.confidence = confidence;
            this.boundingBox = boundingBox;
        }
        
        // Getters and Setters
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        
        public float getConfidence() { return confidence; }
        public void setConfidence(float confidence) { this.confidence = confidence; }
        
        public Rectangle getBoundingBox() { return boundingBox; }
        public void setBoundingBox(Rectangle boundingBox) { this.boundingBox = boundingBox; }
        
        public List<WordResult> getWords() { return words; }
        public void setWords(List<WordResult> words) { this.words = words; }
        
        @Override
        public String toString() {
            return "LineResult{text='" + text + "', confidence=" + confidence + ", boundingBox=" + boundingBox + '}';
        }
    }
}
